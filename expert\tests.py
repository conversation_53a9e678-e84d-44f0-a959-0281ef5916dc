from django.test import TestCase
from rest_framework.test import APITestCase
from rest_framework import status
from django.contrib.auth import get_user_model
from .models import Expert
from .serializers import ExpertSerializer

User = get_user_model()


class ExpertSerializerTest(TestCase):
    """Test cases for ExpertSerializer"""

    def test_serializer_fields(self):
        """Test that serializer includes all expected fields"""
        serializer = ExpertSerializer()
        expected_fields = {
            'id', 'code', 'name', 'affiliation', 'specialization',
            'experienceYears', 'phone', 'email', 'certifications',
            'remark', 'status', 'create_user_id', 'create_date',
            'update_user_id', 'update_date', 'createUser', 'updateUser'
        }
        self.assertEqual(set(serializer.fields.keys()), expected_fields)


class ExpertViewSetTest(APITestCase):
    """Test cases for ExpertViewSet"""

    def setUp(self):
        """Set up test data"""
        self.user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            firstname='Test',
            lastname='User'
        )

    def test_expert_list_endpoint(self):
        """Test that the expert list endpoint is accessible"""
        response = self.client.get('/api/experts/')
        # Should return 200 for read-only access
        self.assertEqual(response.status_code, status.HTTP_200_OK)

    def test_expert_detail_endpoint_structure(self):
        """Test that the expert detail endpoint structure is correct"""
        # This test just checks the endpoint exists and returns proper response structure
        response = self.client.get('/api/experts/')
        self.assertIn('results', response.data)
        self.assertIn('count', response.data)
