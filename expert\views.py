from rest_framework import viewsets
from rest_framework.permissions import IsAuthenticatedOrReadOnly
from django_filters.rest_framework import DjangoFilterBackend
from rest_framework import filters
from drf_spectacular.utils import extend_schema
from utils.pagination import CustomPagination
from .models import Expert
from .serializers import ExpertSerializer


@extend_schema(
    tags=["Expert"]
)
class ExpertViewSet(viewsets.ModelViewSet):
    """
    ViewSet for Expert model providing CRUD operations.
    """
    queryset = Expert.objects.all()
    serializer_class = ExpertSerializer
    pagination_class = CustomPagination
    permission_classes = [IsAuthenticatedOrReadOnly]
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter, filters.SearchFilter]
    filterset_fields = ['code', 'specialization', 'status', 'affiliation']
    search_fields = ['name', 'code', 'specialization', 'affiliation', 'email']
    ordering_fields = ['name', 'code', 'experienceYears', 'created_at']
