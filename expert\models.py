from django.db import models
from RTRDA.model import BaseModel


class Expert(BaseModel):
    code = models.CharField(db_column='Code', max_length=100, db_collation='Thai_CI_AI')
    name = models.Char<PERSON>ield(db_column='Name', max_length=500, db_collation='Thai_CI_AI')
    affiliation = models.CharField(db_column='Affiliation', max_length=500, db_collation='Thai_CI_AI', blank=True, null=True)
    specialization = models.CharField(db_column='Specialization', max_length=500, db_collation='Thai_CI_AI')
    experienceyears = models.IntegerField(db_column='ExperienceYears')
    phone = models.CharField(db_column='Phone', max_length=500, db_collation='Thai_CI_AI')
    email = models.Char<PERSON>ield(db_column='Email', max_length=500, db_collation='Thai_CI_AI')
    certifications = models.Char<PERSON><PERSON>(db_column='Certifications', max_length=100, db_collation='Thai_CI_AI', blank=True, null=True)
    remark = models.TextField(db_column='Remark', db_collation='Thai_CI_AI', blank=True, null=True)
    status = models.BooleanField(db_column='Status')

    class Meta:
        managed = False
        db_table = 'Expert'
