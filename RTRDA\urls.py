"""
URL configuration for RTRDA project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.1/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""

from django.contrib import admin
from django.urls import path, include
from rest_framework_simplejwt.views import (
    TokenObtainPairView,
)
from rest_framework.routers import DefaultRouter
from drf_spectacular.views import SpectacularAPIView, SpectacularSwaggerView, SpectacularRedocView
from rest_framework.permissions import AllowAny
from users.views import *
from mas.views import *
from news.views import *
from trainings.views import *
from departments.views import DepartmentViewSet, ExternalDataViewSet
from website_statistics.views import WebsitestatisticsViewSet
from manufacturer.views import ManufacturerViewSet
from rails.views import RailServiceProviderViewSet, RailServiceProviderMasRailwayLineViewSet, RailwayEngineeringStandardViewSet, AgencySupportRailwayResearchViewSet, UnderConstructionViewSet
from testings.views import TestingViewSet, TestingCenterAgencyViewSet, TestingCenterViewSet, ServiceViewSet, StandardTestingCenterViewSet
from download.views import DownloadRequesterViewSet, DownloadRequesterDetailViewSet
from course.views import CourseViewSet
from research.views import ResearchViewSet, ResearcherViewSet, ResearchAgencyViewSet, ResearchResearchAgencyViewSet, ResearchResearcherViewSet
from survey.views import SurveyViewSet
from components.views import (
    ComponentTypeMainViewSet, ComponentTypeSubViewSet, ComponentTypeViewSet,
    ComponentGroupViewSet, ComponentViewSet, StandardComponentViewSet,
    TestingCenterComponentViewSet, ManufacturerComponentViewSet
)
from django.conf import settings
from django.conf.urls.static import static
from .views import CustomTokenRefreshView
from api.views import SearchViewSet
from datacatalog.views import DataCatalogViewSet
from owner_project.views import OwnerProjectViewSet, OwnerProjectMasRailwayLineViewSet
from mongodb_app.views import (
    ComponentSrcViewSet,
)
from service_fares.views import ServicefaresViewSet, RouteViewSet
from safety.views import SafetyViewSet
from maintenance.views import MaintenanceWorkViewSet
from manpower.views import ManpowerQualificationsViewSet
from environment.views import EnvironmentViewSet

router = DefaultRouter()
router.register(r'users', UserViewSet)
router.register(r'news', NewsViewSet)
router.register(r'news-category', NewsCategoryViewSet)
router.register(r'announcements', AnnouncementViewSet)
router.register(r'mas/province', MasProvinceViewSet)
router.register(r'mas/position', MasPositionViewSet)
router.register(r'mas/district', MasDistrictViewSet)
router.register(r'mas/course-type', MasCourseTypeViewSet)
router.register(r'mas/age-range', MasAgeRangeViewSet)
router.register(r'mas/geography', MasGeographyViewSet)
router.register(r'mas/standard-agency', MasStandardAgencyViewSet)
router.register(r'mas/rail-component-type', MasRailComponentTypeViewSet)
router.register(r'mas/railway-line', MasRailwayLineViewSet)
router.register(r'mas/train-type', MasTrainTypeViewSet)
router.register(r'mas/usage-purpose', MasUsagePurposeViewSet)
router.register(r'mas/user-type', MasUserTypeViewSet)
router.register(r'mas/type-of-work', MasTypeOfWorkViewSet)
router.register(r'mas/subdistrict', MasSubdistrictViewSet)
router.register(r'mas/standard', StandardViewSet)
router.register(r'trainings', TrainingViewSet)
router.register(r'training-categories', TrainingCategoryViewSet)
router.register(r'departments', DepartmentViewSet)
router.register(r'external-data', ExternalDataViewSet)
router.register(r'website-statistics', WebsitestatisticsViewSet)
router.register(r'manufacturers', ManufacturerViewSet)
router.register(r'rail-service-providers', RailServiceProviderViewSet)
router.register(r'rail-service-provider-railway-lines', RailServiceProviderMasRailwayLineViewSet)
router.register(r'railway-engineering-standards', RailwayEngineeringStandardViewSet)
router.register(r'agency-support-railway-research', AgencySupportRailwayResearchViewSet)
router.register(r'under-construction', UnderConstructionViewSet)
router.register(r'testing', TestingViewSet)
router.register(r'testing-center-agencies', TestingCenterAgencyViewSet)
router.register(r'testing-centers', TestingCenterViewSet)
router.register(r'testing-services', ServiceViewSet)
router.register(r'standard-testing-centers', StandardTestingCenterViewSet)
router.register(r'download-requesters', DownloadRequesterViewSet)
router.register(r'download-requester-details', DownloadRequesterDetailViewSet)
router.register(r'courses', CourseViewSet)
router.register(r'research', ResearchViewSet)
router.register(r'researchers', ResearcherViewSet)
router.register(r'research-agencies', ResearchAgencyViewSet)
router.register(r'research-research-agencies', ResearchResearchAgencyViewSet)
router.register(r'research-researchers', ResearchResearcherViewSet)
router.register(r'user-logs', UserlogViewSet)
router.register(r'surveys', SurveyViewSet)
router.register(r'search', SearchViewSet, basename='search')
router.register(r'permissions', PermissionViewSet)
router.register(r'roles', RoleViewSet)
router.register(r'permission-roles', PermissionRoleViewSet)
router.register(r'permission-users', PermissionUserViewSet)
router.register(r'component-type-mains', ComponentTypeMainViewSet)
router.register(r'component-type-subs', ComponentTypeSubViewSet)
router.register(r'component-types', ComponentTypeViewSet)
router.register(r'component-groups', ComponentGroupViewSet)
router.register(r'components', ComponentViewSet)
router.register(r'standard-components', StandardComponentViewSet)
router.register(r'testing-center-components', TestingCenterComponentViewSet)
router.register(r'manufacturer-components', ManufacturerComponentViewSet)
router.register(r'data-catalogs', DataCatalogViewSet)
router.register(r'owner-projects', OwnerProjectViewSet)
router.register(r'component-srcs', ComponentSrcViewSet, basename='component-srcs')
router.register(r'owner-project-railway-lines', OwnerProjectMasRailwayLineViewSet)
router.register(r'mas/standard-categories', MasStandardCategoryViewSet)
router.register(r'service-fares', ServicefaresViewSet)
router.register(r'routes', RouteViewSet)
router.register(r'safety', SafetyViewSet)
router.register(r'maintenance-work', MaintenanceWorkViewSet)
router.register(r'manpower-qualifications', ManpowerQualificationsViewSet)
router.register(r'environments', EnvironmentViewSet)

urlpatterns = [
    path("admin/", admin.site.urls),
    path("api/token/", TokenObtainPairView.as_view(), name="token_obtain_pair"),
    path("api/token/refresh/", CustomTokenRefreshView.as_view(), name="token_refresh"),
    path('api/schema/', SpectacularAPIView.as_view(permission_classes=[AllowAny]), name='schema'),
    path('api/swagger/', SpectacularSwaggerView.as_view(url_name='schema'), name='swagger-ui'),
    path('api/redoc/', SpectacularRedocView.as_view(url_name='schema'), name='redoc'),
    path("api/", include(router.urls)),
    path("api/powerbi/", include("power_bi.urls")),
]

# Serve media files in development
# if settings.DEBUG:
urlpatterns += static(settings.MEDIA_PREFIX, document_root=settings.MEDIA_ROOT)
